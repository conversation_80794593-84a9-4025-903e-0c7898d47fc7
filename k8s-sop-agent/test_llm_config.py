#!/usr/bin/env python3
"""
测试新的LLM配置是否正常工作

这个脚本用于验证使用CrewAI内置LLM类替代自定义CustomLLM后，
系统是否能正常工作。
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_llm_import():
    """测试LLM导入是否正常"""
    try:
        from crewai import LLM
        print("✓ CrewAI LLM import successful")
        return True
    except ImportError as e:
        print(f"✗ CrewAI LLM import failed: {e}")
        return False

def test_llm_creation():
    """测试LLM实例创建"""
    try:
        from crewai import LLM
        
        # 测试基本配置
        llm = LLM(
            model="openai/gpt-3.5-turbo",
            temperature=0.7
        )
        print("✓ Basic LLM creation successful")
        
        # 测试带base_url的配置
        llm_with_base = LLM(
            model="openai/test-model",
            base_url="https://api.example.com/v1",
            api_key="test-key",
            temperature=0.7
        )
        print("✓ LLM with custom base_url creation successful")
        
        return True
    except Exception as e:
        print(f"✗ LLM creation failed: {e}")
        return False

def test_crew_import():
    """测试Crew相关导入"""
    try:
        from src.crew import K8sSopCrew
        print("✓ K8sSopCrew import successful")
        return True
    except ImportError as e:
        print(f"✗ K8sSopCrew import failed: {e}")
        return False

def test_crew_initialization():
    """测试Crew初始化（需要环境变量）"""
    try:
        # 检查必需的环境变量
        required_vars = ["OPENAI_API_KEY", "OPENAI_API_BASE", "OPENAI_MODEL_NAME"]
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            print(f"⚠ Missing environment variables: {missing_vars}")
            print("  Please set them in your .env file to test crew initialization")
            return False
        
        from src.crew import K8sSopCrew
        crew_instance = K8sSopCrew()
        print("✓ K8sSopCrew initialization successful")
        print(f"  Using model: {os.getenv('OPENAI_MODEL_NAME')}")
        print(f"  Using endpoint: {os.getenv('OPENAI_API_BASE')}")
        return True
        
    except Exception as e:
        print(f"✗ K8sSopCrew initialization failed: {e}")
        return False

def test_agent_creation():
    """测试Agent创建"""
    try:
        if not all([os.getenv("OPENAI_API_KEY"), os.getenv("OPENAI_API_BASE"), os.getenv("OPENAI_MODEL_NAME")]):
            print("⚠ Skipping agent creation test - missing environment variables")
            return False
            
        from src.crew import K8sSopCrew
        crew_instance = K8sSopCrew()
        
        # 测试创建一个agent
        agent = crew_instance.query_rewriter_agent()
        print("✓ Agent creation successful")
        print(f"  Agent role: {agent.role}")
        return True
        
    except Exception as e:
        print(f"✗ Agent creation failed: {e}")
        return False

def main():
    """运行所有测试"""
    print("Testing new LLM configuration...")
    print("=" * 50)
    
    tests = [
        ("LLM Import", test_llm_import),
        ("LLM Creation", test_llm_creation),
        ("Crew Import", test_crew_import),
        ("Crew Initialization", test_crew_initialization),
        ("Agent Creation", test_agent_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("🎉 All tests passed! The new LLM configuration is working correctly.")
    else:
        print("⚠ Some tests failed. Please check the configuration.")
        
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
