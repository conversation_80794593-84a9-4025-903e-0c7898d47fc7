from torch import Tensor

# defined in torch/csrc/lazy/python/init.cpp
def _mark_step(device: str, devices: list[str], wait: bool) -> None: ...
def _wait_device_ops(devices: list[str]) -> None: ...
def _reset_metrics() -> None: ...
def _counter_names() -> list[str]: ...
def _counter_value(name: str) -> int: ...
def _metrics_report() -> str: ...
def _get_graph_hash(tensors: list[Tensor]) -> str: ...
def _sync_multi(
    tensors: list[Tensor],
    devices: list[str],
    wait: bool = True,
    sync_ltc_data: bool = True,
) -> None: ...
def _get_tensor_id(tensor: Tensor) -> int: ...
def _get_tensors_text(tensors: list[Tensor]) -> str: ...
def _get_tensors_dot(tensors: list[Tensor]) -> str: ...
def _get_tensors_backend(tensors: list[Tensor]) -> str: ...
def _get_force_fallback() -> str: ...
def _set_force_fallback(newval: str) -> None: ...
def _clear_ir_cache() -> None: ...
def _dump_ir_cache(filename: str) -> None: ...
def _set_reuse_ir(val: bool) -> None: ...
def _get_default_device_type() -> str: ...
