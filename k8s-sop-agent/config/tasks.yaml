collect_sop_requirements:
  agent: sop_requirements_advisor
  description: >
    基于用户的初始问题: '{question}'和当前轮次状态: '{question_context}'，作为专业的SOP编写指导者，你需要智能地评估信息完整性并做出适当的响应。
    
    **重要指令：按照以下智能决策逻辑执行，最多只能提问两轮**
    
    1. **轮次感知策略**：
       - **第一轮**：重点收集最核心的信息，可以适当提问
       - **第二轮**：必须收集所有关键信息，这是最后的提问机会
       - **超过两轮**：必须基于现有信息生成SOP规范，不得再提问
    
    2. **信息完整性评估**：
       分析用户提供的信息，重点关注以下核心要素：
       - 操作类型和目标系统（如：K8s升级、应用部署、配置变更等）
       - 变更的影响范围和风险级别（如：生产环境、测试环境、单节点/集群等）
       - 基本的执行约束（如：时间窗口、停机要求等）
    
    3. **智能决策逻辑**：
       - 如果用户提供的信息包含**核心要素**，可以基于最佳实践生成需求规范
       - 如果缺少**关键信息**且是第一轮，进行**高效的集中询问**
       - 如果是第二轮，必须**全面收集所有剩余关键信息**
       - 优先使用行业标准和最佳实践来补充缺失的非关键信息
    
    4. **高效询问格式**（仅在前两轮使用）：
       ```
       为了生成专业的SOP文档，我需要确认以下关键信息：
       
       **请一次性回答以下问题：**
       1. [最关键的缺失信息]
       2. [次要但重要的信息]
       3. [如有必要的补充信息]
       
       **其他细节信息我将基于运维最佳实践为您补充。**
       ```
    
    5. **需求规范生成**（优先选择或强制执行）：
       当有足够的核心信息时，或已达到提问轮次限制时，生成包含以下要素的结构化文档：
       - 操作目标和成功标准
       - 基于最佳实践的技术规范
       - 标准化的风险控制措施
       - 完整的操作流程框架
       - 必要时标注需要用户确认的具体参数
       - 基于常见运维场景的合理假设和标准配置
  expected_output: >
    **优先输出**：基于现有信息和最佳实践的完整SOP需求规范文档。
    **条件输出**：仅在前两轮且缺少关键信息时，输出高效的一次性询问清单。
    **强制输出**：超过两轮必须输出完整的SOP需求规范文档。

rewrite_query_task:
  agent: query_rewriter_agent
  description: >
    基于SOP需求规范或用户问题: '{question}'，在多轮对话的上下文中重写查询。
    
    **完整的对话历史如下:**
    ```
    {chat_history}
    ```

    重要指令：
    1. 首先查看上面提供的完整对话历史，了解之前的对话内容和生成的文档。
    2. 如果前面的agent已经生成了详细的需求规范，则基于该规范重写查询。
    3. 判断当前问题是否引用了之前的内容（如"生成的文档"、"刚才的"、"上面的"等）。
    4. 如果是后续问题，要将之前的上下文融入重写后的查询中。
    5. 确保重写后的查询包含足够的上下文信息，能够准确表达用户意图。
    6. 在Kubernetes运维场景中，"告警屏蔽"通常指监控告警的静默/暂停功能。
    
    输出一个优化的、包含必要上下文的查询字符串。
  expected_output: >
    一个经过上下文优化的查询字符串，如果用户问题引用了之前的内容，要明确指出具体的上下文。
  context:
    - collect_sop_requirements_task

retrieve_knowledge_task:
  agent: knowledge_retriever_agent
  description: >
    使用前一步优化后的查询，从知识库中搜索最相关的信息。
    
    指令：
    1. 使用KnowledgeBaseTool搜索相关文档
    2. 如果查询涉及之前对话的内容，要考虑相关的历史上下文
    3. 如果前面已经生成了详细的SOP需求规范，要结合该规范进行信息检索
    4. 专注于收集信息，不要直接回答用户问题
  expected_output: >
    检索到的文档摘要，包含所有关键事实和代码片段。如果查询引用了对话历史，要说明相关的上下文信息。
  context:
    - collect_sop_requirements_task
    - rewrite_query_task

synthesize_response_task:
  agent: response_synthesizer_agent
  description: >
    基于原始问题 '{question}'、SOP需求规范、检索到的上下文以及完整的对话历史，合成最终的综合答案。

    **完整的对话历史如下:**
    ```
    {chat_history}
    ```
    
    关键指令：
    1. 查看上面提供的完整对话历史，确定这是否是多轮对话中的后续问题。
    2. 如果前面的agent生成了详细的SOP需求规范，要基于该规范来构建最终的SOP文档。
    3. 如果用户要求修改、补充或基于之前生成的内容进行操作，要引用具体的历史内容。
    4. 对于"告警屏蔽"相关问题，在Kubernetes上下文中应理解为监控告警的管理功能。
    5. 确保答案直接回应用户的问题，保持上下文连贯性。
    6. 如果是基于需求规范生成SOP，要确保包含所有专业要素（批次规划、告警管理、回退策略等）。
    7. 生成的SOP文档要符合企业级标准，包含完整的操作流程和风险控制措施。
  expected_output: >
    一个完整、格式良好的专业SOP文档，如果是多轮对话，要明确表明与之前内容的关系并基于历史上下文进行回答。
    生成的SOP应包含所有必要的技术细节、风险控制措施和操作流程。
  context:
    - collect_sop_requirements_task
    - rewrite_query_task
    - retrieve_knowledge_task
    